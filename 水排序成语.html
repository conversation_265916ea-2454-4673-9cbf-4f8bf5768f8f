<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sort Cubes - 方块排序</title>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.80.1/dist/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            overflow: hidden;
            font-family: Arial, sans-serif;
        }
        canvas {
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            height: 100vh;
        }
    </style>
</head>
<body>

<script>
    // 游戏配置 - 竖屏优化
    const config = {
        type: Phaser.AUTO,
        width: 750,
        height: 1334,
        backgroundColor: '#1a237e',
        parent: 'game-container',
        scene: {
            preload: preload,
            create: create,
            update: update
        }
    };

    // 全局游戏变量
    let game;
    let columns = [];
    let selectedColumn = null;
    let timer = 160;
    let timerText;
    let titleText;
    let gameStarted = false;
    let gameWon = false;
    let gameScene = null;
    let currentLevel = 1;
    let levelText;

    const COLUMN_CAPACITY = 4; // 每列最多4个方块
    const CUBE_SIZE = 80; // 适合竖屏的方块大小
    const COLUMN_WIDTH = 110; // 列宽
    const COLUMN_HEIGHT = COLUMN_CAPACITY * CUBE_SIZE + 40;

    // 关卡配置 - 每关有不同的成语组合
    const LEVEL_CONFIG = {
        1: {
            columns: 6,
            emptyColumns: 2,
            time: 160,
            bgm: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
            idioms: {
                yellow: {
                    idiom: "一帆风顺",
                    characters: ["一", "帆", "风", "顺"],
                    colors: { main: 0xFFD700, shadow: 0xDAA520, highlight: 0xFFFF99 }
                },
                blue: {
                    idiom: "心想事成",
                    characters: ["心", "想", "事", "成"],
                    colors: { main: 0x2196F3, shadow: 0x1976D2, highlight: 0xBBDEFB }
                }
            }
        },
        2: {
            columns: 6,
            emptyColumns: 2,
            time: 180,
            bgm: 'https://www.soundjay.com/misc/sounds/bell-ringing-04.wav',
            idioms: {
                green: {
                    idiom: "万事如意",
                    characters: ["万", "事", "如", "意"],
                    colors: { main: 0x4CAF50, shadow: 0x388E3C, highlight: 0xC8E6C9 }
                },
                red: {
                    idiom: "龙马精神",
                    characters: ["龙", "马", "精", "神"],
                    colors: { main: 0xF44336, shadow: 0xD32F2F, highlight: 0xFFCDD2 }
                },
                purple: {
                    idiom: "财源广进",
                    characters: ["财", "源", "广", "进"],
                    colors: { main: 0x9C27B0, shadow: 0x7B1FA2, highlight: 0xE1BEE7 }
                }
            }
        },
        3: {
            columns: 8,
            emptyColumns: 2,
            time: 200,
            bgm: 'https://www.soundjay.com/misc/sounds/bell-ringing-03.wav',
            idioms: {
                yellow: {
                    idiom: "金玉满堂",
                    characters: ["金", "玉", "满", "堂"],
                    colors: { main: 0xFFD700, shadow: 0xDAA520, highlight: 0xFFFF99 }
                },
                blue: {
                    idiom: "福如东海",
                    characters: ["福", "如", "东", "海"],
                    colors: { main: 0x2196F3, shadow: 0x1976D2, highlight: 0xBBDEFB }
                },
                green: {
                    idiom: "寿比南山",
                    characters: ["寿", "比", "南", "山"],
                    colors: { main: 0x4CAF50, shadow: 0x388E3C, highlight: 0xC8E6C9 }
                },
                orange: {
                    idiom: "吉祥如意",
                    characters: ["吉", "祥", "如", "意"],
                    colors: { main: 0xFF9800, shadow: 0xF57C00, highlight: 0xFFE0B2 }
                }
            }
        },
        4: {
            columns: 8,
            emptyColumns: 2,
            time: 220,
            bgm: 'https://www.soundjay.com/misc/sounds/bell-ringing-02.wav',
            idioms: {
                red: {
                    idiom: "恭喜发财",
                    characters: ["恭", "喜", "发", "财"],
                    colors: { main: 0xF44336, shadow: 0xD32F2F, highlight: 0xFFCDD2 }
                },
                purple: {
                    idiom: "步步高升",
                    characters: ["步", "步", "高", "升"],
                    colors: { main: 0x9C27B0, shadow: 0x7B1FA2, highlight: 0xE1BEE7 }
                },
                cyan: {
                    idiom: "年年有余",
                    characters: ["年", "年", "有", "余"],
                    colors: { main: 0x00BCD4, shadow: 0x0097A7, highlight: 0xB2EBF2 }
                },
                pink: {
                    idiom: "花开富贵",
                    characters: ["花", "开", "富", "贵"],
                    colors: { main: 0xE91E63, shadow: 0xC2185B, highlight: 0xF8BBD9 }
                }
            }
        },
        5: {
            columns: 10,
            emptyColumns: 2,
            time: 240,
            bgm: 'https://www.soundjay.com/misc/sounds/bell-ringing-01.wav',
            idioms: {
                yellow: {
                    idiom: "招财进宝",
                    characters: ["招", "财", "进", "宝"],
                    colors: { main: 0xFFD700, shadow: 0xDAA520, highlight: 0xFFFF99 }
                },
                blue: {
                    idiom: "五福临门",
                    characters: ["五", "福", "临", "门"],
                    colors: { main: 0x2196F3, shadow: 0x1976D2, highlight: 0xBBDEFB }
                },
                green: {
                    idiom: "六六大顺",
                    characters: ["六", "六", "大", "顺"],
                    colors: { main: 0x4CAF50, shadow: 0x388E3C, highlight: 0xC8E6C9 }
                },
                red: {
                    idiom: "七星高照",
                    characters: ["七", "星", "高", "照"],
                    colors: { main: 0xF44336, shadow: 0xD32F2F, highlight: 0xFFCDD2 }
                },
                orange: {
                    idiom: "八方来财",
                    characters: ["八", "方", "来", "财"],
                    colors: { main: 0xFF9800, shadow: 0xF57C00, highlight: 0xFFE0B2 }
                }
            }
        },
        6: {
            columns: 10,
            emptyColumns: 2,
            time: 260,
            bgm: 'https://www.soundjay.com/misc/sounds/bell-ringing-06.wav',
            idioms: {
                yellow: {
                    idiom: "九九归一",
                    characters: ["九", "九", "归", "一"],
                    colors: { main: 0xFFD700, shadow: 0xDAA520, highlight: 0xFFFF99 }
                },
                blue: {
                    idiom: "十全十美",
                    characters: ["十", "全", "十", "美"],
                    colors: { main: 0x2196F3, shadow: 0x1976D2, highlight: 0xBBDEFB }
                },
                green: {
                    idiom: "百年好合",
                    characters: ["百", "年", "好", "合"],
                    colors: { main: 0x4CAF50, shadow: 0x388E3C, highlight: 0xC8E6C9 }
                },
                red: {
                    idiom: "千里迢迢",
                    characters: ["千", "里", "迢", "迢"],
                    colors: { main: 0xF44336, shadow: 0xD32F2F, highlight: 0xFFCDD2 }
                },
                purple: {
                    idiom: "万紫千红",
                    characters: ["万", "紫", "千", "红"],
                    colors: { main: 0x9C27B0, shadow: 0x7B1FA2, highlight: 0xE1BEE7 }
                },
                orange: {
                    idiom: "锦上添花",
                    characters: ["锦", "上", "添", "花"],
                    colors: { main: 0xFF9800, shadow: 0xF57C00, highlight: 0xFFE0B2 }
                }
            }
        }
    };

    // 当前关卡的成语数据（从关卡配置中获取）
    let currentIdioms = {};
    let currentIdiomNames = [];

    function preload() {
        // 预加载阶段 - 现在我们动态创建方块，所以这里不需要预先生成纹理
        // 加载BGM
        const levelConfig = LEVEL_CONFIG[currentLevel];
        if (levelConfig && levelConfig.bgm) {
            this.load.audio(`bgm_${currentLevel}`, levelConfig.bgm);
        }
    }

    function create() {
        gameScene = this;

        // 背景渐变 - 竖屏优化
        const bg = this.add.graphics();
        bg.fillGradientStyle(0x1a237e, 0x3949ab, 0x1a237e, 0x3949ab);
        bg.fillRect(0, 0, config.width, config.height);

        // 标题 - 顶部居中位置
        titleText = this.add.text(config.width/2, 60, '成语排序', {
            fontSize: '32px',
            fontFamily: 'Arial, "Microsoft YaHei", "SimHei", sans-serif',
            fontWeight: 'bold',
            fill: '#ffffff',
            align: 'center',
            stroke: '#000000',
            strokeThickness: 4
        }).setOrigin(0.5);

        // 关卡显示
        levelText = this.add.text(config.width/2, 95, `第 ${currentLevel} 关`, {
            fontSize: '20px',
            fontFamily: 'Arial, "Microsoft YaHei", "SimHei", sans-serif',
            fontWeight: 'bold',
            fill: '#FFD700',
            align: 'center',
            stroke: '#000000',
            strokeThickness: 2
        }).setOrigin(0.5);

        // 游戏说明
        const instructionText = this.add.text(config.width/2, 135, '点击列移动方块，聚集相同成语的字\n同色列每次只移动一个方块\n从上到下按正确顺序排列的成语会自动消除', {
            fontSize: '13px',
            fontFamily: 'Arial, "Microsoft YaHei", "SimHei", sans-serif',
            fontWeight: 'normal',
            fill: '#e3f2fd',
            alpha: 0.8,
            align: 'center'
        }).setOrigin(0.5);

        // 计时器背景 - 顶部右侧位置
        const timerBg = this.add.graphics();
        timerBg.fillStyle(0xffffff, 0.9);
        timerBg.fillRoundedRect(config.width - 140, 30, 120, 50, 12);
        timerBg.lineStyle(2, 0x333333);
        timerBg.strokeRoundedRect(config.width - 140, 30, 120, 50, 12);

        // 计时器标签
        this.add.text(config.width - 80, 42, 'TIME', {
            fontSize: '12px',
            fontWeight: 'bold',
            fill: '#666666'
        }).setOrigin(0.5);

        // 计时器文本
        timerText = this.add.text(config.width - 80, 65, timer.toString(), {
            fontSize: '24px',
            fontWeight: 'bold',
            fill: '#333333'
        }).setOrigin(0.5);

        // 获取当前关卡配置
        const levelConfig = LEVEL_CONFIG[currentLevel] || LEVEL_CONFIG[1];

        // 初始化当前关卡的计时器
        timer = levelConfig.time;

        // 初始化当前关卡的成语数据
        currentIdioms = levelConfig.idioms;
        currentIdiomNames = Object.keys(currentIdioms);

        // 进度条背景
        const progressBg = this.add.graphics();
        progressBg.fillStyle(0x37474f, 0.8);
        progressBg.fillRoundedRect(config.width/2 - 150, config.height - 80, 300, 16, 8);

        // 进度条
        this.progressBar = this.add.graphics();

        // 播放BGM
        if (levelConfig.bgm) {
            try {
                const bgm = this.sound.add(`bgm_${currentLevel}`, { loop: true, volume: 0.3 });
                bgm.play();
            } catch (e) {
                console.log('BGM加载失败:', e);
            }
        }

        // 初始化关卡
        initializeLevel();

        // 初始化进度条
        updateProgressBar();

        // 输入处理
        this.input.on('pointerdown', function(pointer) {
            handleInput(pointer);
        });

        // 启动计时器
        this.time.addEvent({
            delay: 1000,
            callback: function() {
                updateTimer();
            },
            loop: true
        });
    }

    function initializeLevel() {
        // 清除现有列
        columns.forEach(col => col.destroy());
        columns = [];

        // 获取当前关卡配置
        const levelConfig = LEVEL_CONFIG[currentLevel] || LEVEL_CONFIG[1];
        const numColumns = levelConfig.columns;
        const numColors = currentIdiomNames.length; // 使用当前关卡的成语数量
        const cubesPerColor = 4; // 每种颜色4个方块
        const emptyColumns = levelConfig.emptyColumns;

        // 生成关卡数据
        const levelData = generateRandomLevel(numColumns, numColors, cubesPerColor, emptyColumns);

        // 计算列的位置 - 竖屏布局优化，上下各3列
        const columnsPerRow = 3;
        const totalWidth = columnsPerRow * COLUMN_WIDTH;
        const startX = (config.width - totalWidth) / 2 + COLUMN_WIDTH/2;

        // 上排3列
        const topRowY = config.height/2 - 100;
        // 下排3列
        const bottomRowY = config.height/2 + 300;

        levelData.forEach((cubes, index) => {
            let x, y;
            if (index < 3) {
                // 上排
                x = startX + (index * COLUMN_WIDTH);
                y = topRowY;
            } else {
                // 下排
                x = startX + ((index - 3) * COLUMN_WIDTH);
                y = bottomRowY;
            }
            const column = new CubeColumn(gameScene, x, y, COLUMN_CAPACITY, cubes);
            columns.push(column);
        });
    }

    function generateRandomLevel(numColumns, numColors, cubesPerColor, emptyColumns) {
        let allCubes = [];
        const availableIdioms = Phaser.Utils.Array.Shuffle(currentIdiomNames).slice(0, numColors);

        // 生成所有方块 - 每个成语的每个字符
        availableIdioms.forEach(idiomName => {
            const idiom = currentIdioms[idiomName];
            idiom.characters.forEach(character => {
                allCubes.push({ idiom: idiomName, character: character });
            });
        });

        // 确保参数合理性
        const filledColumns = numColumns - emptyColumns;
        const totalCubes = allCubes.length;

        // 检查是否能合理分配
        if (filledColumns <= 0) {
            console.warn("没有足够的非空列，调整为至少1个非空列");
            emptyColumns = numColumns - 1;
        }

        const maxCubesPerColumn = COLUMN_CAPACITY;
        if (totalCubes > filledColumns * maxCubesPerColumn) {
            console.warn("方块总数超过容量，减少方块数量");
            // 重新计算合理的方块数量
            const reasonableCubesPerColor = Math.floor((filledColumns * maxCubesPerColumn) / numColors);
            allCubes = [];
            availableColors.forEach(color => {
                for (let i = 0; i < reasonableCubesPerColor; i++) {
                    allCubes.push(color);
                }
            });
        }

        Phaser.Utils.Array.Shuffle(allCubes);

        const level = [];
        const actualFilledColumns = numColumns - emptyColumns;

        // 尽量均匀分配方块到各列
        let cubeIndex = 0;
        for (let i = 0; i < actualFilledColumns; i++) {
            const columnCubes = [];
            const cubesForThisColumn = Math.ceil((allCubes.length - cubeIndex) / (actualFilledColumns - i));

            for (let j = 0; j < cubesForThisColumn && cubeIndex < allCubes.length; j++) {
                columnCubes.push(allCubes[cubeIndex++]);
            }
            level.push(columnCubes);
        }

        // 添加空列
        for (let i = 0; i < emptyColumns; i++) {
            level.push([]);
        }

        return Phaser.Utils.Array.Shuffle(level);
    }

    function handleInput(pointer) {
        if (gameWon || timer <= 0) return;

        let clickedColumn = null;

        for (let column of columns) {
            if (column.getBounds().contains(pointer.x, pointer.y)) {
                clickedColumn = column;
                break;
            }
        }

        if (clickedColumn) {
            if (selectedColumn === null) {
                if (!clickedColumn.isEmpty()) {
                    selectedColumn = clickedColumn;
                    selectedColumn.highlight();
                }
            } else if (selectedColumn === clickedColumn) {
                selectedColumn.unhighlight();
                selectedColumn = null;
            } else {
                const moveSuccessful = tryMoveCubes(selectedColumn, clickedColumn);
                if (moveSuccessful) {
                    gameScene.time.delayedCall(300, function() {
                        checkWinCondition();
                    });
                }
                selectedColumn.unhighlight();
                selectedColumn = null;
            }
        }
    }

    function tryMoveCubes(sourceColumn, targetColumn) {
        if (sourceColumn.isEmpty()) return false;

        const { cubes: topCubesToMove, idiom: topIdiomToMove } = sourceColumn.getTopCubesInfo();
        const targetTopIdiom = targetColumn.getTopIdiom();
        const targetEmptySpace = targetColumn.getEmptySpace();

        // 特殊情况：如果源列是同色列，只移动一个方块
        if (sourceColumn.isSameIdiomColumn() && topCubesToMove.length > 1) {
            // 只移动最顶部的一个方块
            const singleCube = [topCubesToMove[topCubesToMove.length - 1]];

            // 检查目标列是否可以接收这个方块
            if (!targetColumn.isEmpty() && targetTopIdiom !== topIdiomToMove) {
                return false;
            }

            if (targetEmptySpace < 1) {
                return false;
            }

            sourceColumn.removeCubes(1);
            targetColumn.addCubes(singleCube);
            return true;
        }

        // 检查移动规则 - 只有相同成语的字符可以叠放
        if (!targetColumn.isEmpty() && targetTopIdiom !== topIdiomToMove) {
            return false;
        }

        if (targetEmptySpace < topCubesToMove.length) {
            return false;
        }

        // 执行移动
        sourceColumn.removeCubes(topCubesToMove.length);
        targetColumn.addCubes(topCubesToMove);

        return true;
    }

    function checkWinCondition() {
        // 调试：输出每一列的成语数据
        console.log("=== 检查每一列的状态 ===");
        columns.forEach((column, index) => {
            if (!column.isEmpty()) {
                const cubeData = column.cubes.map(cube => cube.character).join('');
                const idiom = column.cubes[0].idiom;
                const expectedOrder = IDIOMS[idiom].characters.join('');
                const isComplete = column.isSortedAndFull();

                console.log(`列 ${index + 1}:`);
                console.log(`  成语: ${IDIOMS[idiom].idiom}`);
                console.log(`  当前顺序: ${cubeData}`);
                console.log(`  正确顺序: ${expectedOrder}`);
                console.log(`  是否完成: ${isComplete}`);
                console.log(`  方块数量: ${column.cubes.length}`);
                console.log(`  是否同色: ${column.isSameIdiomColumn()}`);
                console.log('---');
            } else {
                console.log(`列 ${index + 1}: 空列`);
            }
        });

        // 首先检查是否有完成的列需要消除
        let hasCompletedColumns = false;
        columns.forEach((column, index) => {
            if (column.isSortedAndFull()) {
                console.log(`发现完成的列: ${index + 1}`);
                hasCompletedColumns = true;
                // 延迟消除，让玩家看到完成效果
                gameScene.time.delayedCall(1500, function() {
                    eliminateCompletedColumn(column);
                });
            }
        });

        // 如果有完成的列，不立即检查胜利条件
        if (hasCompletedColumns) {
            console.log("有完成的列，等待消除...");
            return;
        }

        // 检查是否所有方块都被消除了
        let hasAnyBlocks = false;
        for (let column of columns) {
            if (!column.isEmpty()) {
                hasAnyBlocks = true;
                break;
            }
        }

        if (!hasAnyBlocks) {
            gameWon = true;
            console.log("所有方块都被消除，游戏胜利！");
            // 添加胜利粒子效果
            createWinParticles();
            showWinMessage();
        }
    }

    function eliminateCompletedColumn(column) {
        if (!column.isSortedAndFull()) return;

        // 创建消除动画
        const cubeSprites = [...column.cubeSprites];

        // 添加消除粒子效果
        createEliminationParticles(column);

        // 逐个消除方块
        cubeSprites.forEach((sprite, index) => {
            gameScene.time.delayedCall(index * 100, function() {
                // 消除动画
                gameScene.tweens.add({
                    targets: sprite,
                    alpha: 0,
                    scaleX: 1.5,
                    scaleY: 1.5,
                    rotation: Math.PI * 2,
                    duration: 400,
                    ease: 'Back.easeIn',
                    onComplete: () => {
                        sprite.destroy();
                    }
                });
            });
        });

        // 清空列数据
        gameScene.time.delayedCall(cubeSprites.length * 100 + 400, function() {
            column.cubes = [];
            column.cubeSprites = [];
            column.updateCompletionHighlight();

            // 重新检查胜利条件
            checkWinCondition();
        });
    }

    function createEliminationParticles(column) {
        // 在列的位置创建消除粒子效果
        for (let i = 0; i < 30; i++) {
            const particle = gameScene.add.graphics();
            const colors = [0xFFD700, 0xFF6B6B, 0x4ECDC4, 0x45B7D1, 0x96CEB4, 0xFFFFFF];
            const color = colors[Math.floor(Math.random() * colors.length)];

            particle.fillStyle(color);
            particle.fillCircle(0, 0, Math.random() * 6 + 2);

            particle.x = column.x + (Math.random() - 0.5) * COLUMN_WIDTH;
            particle.y = column.y - COLUMN_HEIGHT/2 + (Math.random() - 0.5) * COLUMN_HEIGHT;

            gameScene.tweens.add({
                targets: particle,
                y: particle.y - Math.random() * 150 - 50,
                x: particle.x + (Math.random() - 0.5) * 100,
                alpha: 0,
                scaleX: 0.2,
                scaleY: 0.2,
                duration: Math.random() * 1000 + 800,
                ease: 'Cubic.easeOut',
                onComplete: () => particle.destroy()
            });
        }
    }

    function createWinParticles() {
        // 创建庆祝粒子效果
        for (let i = 0; i < 50; i++) {
            const particle = gameScene.add.graphics();
            const colors = [0xFFD700, 0xFF6B6B, 0x4ECDC4, 0x45B7D1, 0x96CEB4];
            const color = colors[Math.floor(Math.random() * colors.length)];

            particle.fillStyle(color);
            particle.fillCircle(0, 0, Math.random() * 8 + 4);

            particle.x = Math.random() * config.width;
            particle.y = Math.random() * config.height;

            gameScene.tweens.add({
                targets: particle,
                y: particle.y + Math.random() * 200 + 100,
                x: particle.x + (Math.random() - 0.5) * 200,
                alpha: 0,
                rotation: Math.random() * Math.PI * 2,
                duration: Math.random() * 2000 + 1000,
                ease: 'Cubic.easeOut',
                onComplete: () => particle.destroy()
            });
        }
    }

    function showWinMessage() {
        // 创建胜利背景
        const winBg = gameScene.add.graphics();
        winBg.fillStyle(0x000000, 0.8);
        winBg.fillRect(0, 0, config.width, config.height);

        // 庆祝表情
        const celebrationEmoji = gameScene.add.text(config.width/2, config.height/2 - 120, '🎉', {
            fontSize: '80px'
        }).setOrigin(0.5);

        const winText = gameScene.add.text(config.width/2, config.height/2 - 50, '恭喜过关！', {
            fontSize: '42px',
            fontFamily: 'Arial, "Microsoft YaHei", "SimHei", sans-serif',
            fontWeight: 'bold',
            fill: '#FFD700',
            stroke: '#000000',
            strokeThickness: 6
        }).setOrigin(0.5);

        const subText = gameScene.add.text(config.width/2, config.height/2 + 10, '所有成语都被成功消除了！', {
            fontSize: '20px',
            fontFamily: 'Arial, "Microsoft YaHei", "SimHei", sans-serif',
            fontWeight: 'normal',
            fill: '#ffffff',
            stroke: '#000000',
            strokeThickness: 2
        }).setOrigin(0.5);

        // 检查是否还有下一关
        const hasNextLevel = LEVEL_CONFIG[currentLevel + 1];

        if (hasNextLevel) {
            // 下一关按钮
            const nextLevelBg = gameScene.add.graphics();
            nextLevelBg.fillStyle(0x4CAF50, 0.9);
            nextLevelBg.fillRoundedRect(config.width/2 - 100, config.height/2 + 60, 200, 50, 25);
            nextLevelBg.lineStyle(3, 0x2E7D32);
            nextLevelBg.strokeRoundedRect(config.width/2 - 100, config.height/2 + 60, 200, 50, 25);

            const nextLevelText = gameScene.add.text(config.width/2, config.height/2 + 85, '下一关', {
                fontSize: '24px',
                fontFamily: 'Arial, "Microsoft YaHei", "SimHei", sans-serif',
                fontWeight: 'bold',
                fill: '#ffffff'
            }).setOrigin(0.5);

            // 重新开始按钮
            const restartBg = gameScene.add.graphics();
            restartBg.fillStyle(0x2196F3, 0.9);
            restartBg.fillRoundedRect(config.width/2 - 100, config.height/2 + 130, 200, 50, 25);
            restartBg.lineStyle(3, 0x1976D2);
            restartBg.strokeRoundedRect(config.width/2 - 100, config.height/2 + 130, 200, 50, 25);

            const restartText = gameScene.add.text(config.width/2, config.height/2 + 155, '重新开始', {
                fontSize: '20px',
                fontFamily: 'Arial, "Microsoft YaHei", "SimHei", sans-serif',
                fontWeight: 'bold',
                fill: '#ffffff'
            }).setOrigin(0.5);

            // 按钮交互
            nextLevelBg.setInteractive(new Phaser.Geom.Rectangle(config.width/2 - 100, config.height/2 + 60, 200, 50), Phaser.Geom.Rectangle.Contains);
            nextLevelBg.on('pointerdown', function() {
                currentLevel++;
                levelText.setText(`第 ${currentLevel} 关`);
                gameWon = false;

                // 停止当前BGM
                gameScene.sound.stopAll();

                // 更新当前关卡的成语数据
                const levelConfig = LEVEL_CONFIG[currentLevel];
                currentIdioms = levelConfig.idioms;
                currentIdiomNames = Object.keys(currentIdioms);

                // 播放新关卡BGM
                if (levelConfig.bgm) {
                    try {
                        // 预加载新BGM
                        gameScene.load.audio(`bgm_${currentLevel}`, levelConfig.bgm);
                        gameScene.load.start();
                        gameScene.load.once('complete', function() {
                            const bgm = gameScene.sound.add(`bgm_${currentLevel}`, { loop: true, volume: 0.3 });
                            bgm.play();
                        });
                    } catch (e) {
                        console.log('BGM加载失败:', e);
                    }
                }

                // 清理界面
                winBg.destroy();
                celebrationEmoji.destroy();
                winText.destroy();
                subText.destroy();
                nextLevelBg.destroy();
                nextLevelText.destroy();
                restartBg.destroy();
                restartText.destroy();

                // 初始化新关卡
                initializeLevel();
                timer = levelConfig.time;
                timerText.setText(timer.toString());
            });

            restartBg.setInteractive(new Phaser.Geom.Rectangle(config.width/2 - 100, config.height/2 + 130, 200, 50), Phaser.Geom.Rectangle.Contains);
            restartBg.on('pointerdown', function() {
                location.reload();
            });
        } else {
            // 游戏完成
            const completeBg = gameScene.add.graphics();
            completeBg.fillStyle(0xFF9800, 0.9);
            completeBg.fillRoundedRect(config.width/2 - 100, config.height/2 + 60, 200, 50, 25);
            completeBg.lineStyle(3, 0xF57C00);
            completeBg.strokeRoundedRect(config.width/2 - 100, config.height/2 + 60, 200, 50, 25);

            const completeText = gameScene.add.text(config.width/2, config.height/2 + 85, '游戏完成！', {
                fontSize: '20px',
                fontFamily: 'Arial, "Microsoft YaHei", "SimHei", sans-serif',
                fontWeight: 'bold',
                fill: '#ffffff'
            }).setOrigin(0.5);

            completeBg.setInteractive(new Phaser.Geom.Rectangle(config.width/2 - 100, config.height/2 + 60, 200, 50), Phaser.Geom.Rectangle.Contains);
            completeBg.on('pointerdown', function() {
                location.reload();
            });
        }

        // 动画效果
        gameScene.tweens.add({
            targets: celebrationEmoji,
            scaleX: 1.2,
            scaleY: 1.2,
            duration: 600,
            yoyo: true,
            repeat: -1,
            ease: 'Sine.easeInOut'
        });

        gameScene.tweens.add({
            targets: winText,
            scaleX: 1.05,
            scaleY: 1.05,
            duration: 800,
            yoyo: true,
            repeat: -1,
            ease: 'Sine.easeInOut'
        });
    }

    function updateTimer() {
        if (gameWon || timer <= 0) return;
        
        timer--;
        timerText.setText(timer.toString());
        
        if (timer <= 0) {
            showGameOverMessage();
        }
    }

    function showGameOverMessage() {
        // 创建游戏结束背景
        const gameOverBg = gameScene.add.graphics();
        gameOverBg.fillStyle(0x000000, 0.8);
        gameOverBg.fillRect(0, 0, config.width, config.height);

        const gameOverText = gameScene.add.text(config.width/2, config.height/2 - 40, '时间到！', {
            fontSize: '48px',
            fontFamily: 'Arial, "Microsoft YaHei", "SimHei", sans-serif',
            fontWeight: 'bold',
            fill: '#FF4444',
            stroke: '#000000',
            strokeThickness: 6
        }).setOrigin(0.5);

        const retryText = gameScene.add.text(config.width/2, config.height/2 + 40, '点击重新开始', {
            fontSize: '20px',
            fontFamily: 'Arial, "Microsoft YaHei", "SimHei", sans-serif',
            fontWeight: 'normal',
            fill: '#ffffff',
            stroke: '#000000',
            strokeThickness: 2
        }).setOrigin(0.5);

        // 添加重启功能
        gameScene.input.once('pointerdown', function() {
            location.reload();
        });
    }

    function updateProgressBar() {
        if (!gameScene || !gameScene.progressBar) return;

        gameScene.progressBar.clear();

        // 计算完成进度
        let totalColumns = 0;
        let completedColumns = 0;

        columns.forEach(column => {
            if (!column.isEmpty()) {
                totalColumns++;
                if (column.isSortedAndFull()) {
                    completedColumns++;
                }
            }
        });

        const progress = totalColumns > 0 ? completedColumns / totalColumns : 0;
        const progressWidth = 296 * progress; // 300 - 4 for padding

        // 绘制进度条
        if (progressWidth > 0) {
            const gradient = progress < 0.5 ? 0xff6b6b : progress < 0.8 ? 0xffd93d : 0x6bcf7f;
            gameScene.progressBar.fillStyle(gradient, 0.9);
            gameScene.progressBar.fillRoundedRect(config.width/2 - 148, config.height - 78, progressWidth, 12, 6);
        }
    }

    function update() {
        // 游戏主循环
        updateProgressBar();

        // 更新所有列的完成状态高亮
        columns.forEach(column => {
            column.updateCompletionHighlight();
        });
    }

    // CubeColumn 类 - 方块列容器
    class CubeColumn extends Phaser.GameObjects.Container {
        constructor(scene, x, y, capacity, initialCubes) {
            super(scene, x, y);
            scene.add.existing(this);

            this.capacity = capacity;
            this.cubes = initialCubes || [];
            this.cubeSprites = [];

            // 绘制列的底座
            this.drawBase();

            // 创建方块精灵
            this.updateCubeVisuals();

            // 设置交互区域
            this.setSize(COLUMN_WIDTH, COLUMN_HEIGHT);
            this.setInteractive();
        }

        drawBase() {
            // 绘制列的底座和边框 - 竖屏优化
            this.baseGraphics = this.scene.add.graphics();

            // 底座 - 适合竖屏的设计
            this.baseGraphics.fillStyle(0x37474f, 0.9);
            this.baseGraphics.fillRoundedRect(-COLUMN_WIDTH/2, 0, COLUMN_WIDTH, 35, 10);

            // 底座阴影效果
            this.baseGraphics.fillStyle(0x263238, 0.6);
            this.baseGraphics.fillRoundedRect(-COLUMN_WIDTH/2 + 2, 2, COLUMN_WIDTH - 4, 33, 8);

            // 边框指示器 - 适合竖屏的视觉效果
            this.baseGraphics.lineStyle(3, 0x607d8b, 0.8);
            this.baseGraphics.strokeRoundedRect(-COLUMN_WIDTH/2 + 6, -COLUMN_HEIGHT + 35, COLUMN_WIDTH - 12, COLUMN_HEIGHT - 35, 6);

            // 内部虚线指示器
            this.baseGraphics.lineStyle(2, 0x90a4ae, 0.4);
            for (let i = 1; i < this.capacity; i++) {
                const y = -i * CUBE_SIZE;
                this.baseGraphics.lineBetween(-COLUMN_WIDTH/2 + 10, y, COLUMN_WIDTH/2 - 10, y);
            }

            this.add(this.baseGraphics);
        }

        updateCubeVisuals() {
            // 清除现有方块精灵
            this.cubeSprites.forEach(sprite => sprite.destroy());
            this.cubeSprites = [];

            // 从底部向上创建方块精灵
            for (let i = 0; i < this.cubes.length; i++) {
                const cube = this.cubes[i];
                const idiom = currentIdioms[cube.idiom];
                const colors = idiom.colors;

                // 创建方块容器
                const cubeContainer = this.scene.add.container(0, -i * CUBE_SIZE - CUBE_SIZE/2);

                // 创建方块图形
                const graphics = this.scene.add.graphics();

                // 绘制3D效果的方块
                graphics.fillStyle(colors.main);
                graphics.fillRoundedRect(-CUBE_SIZE/2, -CUBE_SIZE/2, CUBE_SIZE, CUBE_SIZE, 8);

                graphics.fillStyle(colors.shadow);
                graphics.fillRoundedRect(-CUBE_SIZE/2 + 4, -CUBE_SIZE/2 + 4, CUBE_SIZE-8, CUBE_SIZE-8, 6);

                graphics.fillStyle(colors.highlight);
                graphics.fillRoundedRect(-CUBE_SIZE/2 + 8, -CUBE_SIZE/2 + 8, CUBE_SIZE-16, CUBE_SIZE-16, 4);

                graphics.fillStyle(colors.main);
                graphics.fillRoundedRect(-CUBE_SIZE/2 + 12, -CUBE_SIZE/2 + 12, CUBE_SIZE-24, CUBE_SIZE-24, 3);

                // 创建汉字文本
                const text = this.scene.add.text(0, 0, cube.character, {
                    fontSize: '28px',
                    fontFamily: 'Arial, "Microsoft YaHei", "SimHei", sans-serif',
                    fontWeight: 'bold',
                    fill: '#000000',
                    stroke: '#ffffff',
                    strokeThickness: 1
                }).setOrigin(0.5);

                // 将图形和文本添加到容器
                cubeContainer.add([graphics, text]);

                // 添加轻微的随机旋转和缩放效果 - 竖屏优化
                const randomRotation = (Math.random() - 0.5) * 0.06;
                const randomScale = 0.97 + Math.random() * 0.06;
                cubeContainer.setRotation(randomRotation);
                cubeContainer.setScale(randomScale);

                // 添加深度效果
                const depthOffset = (Math.random() - 0.5) * 3;
                cubeContainer.x = depthOffset;

                this.add(cubeContainer);
                this.cubeSprites.push(cubeContainer);
            }

            // 更新完成状态高亮
            this.updateCompletionHighlight();
        }

        getTopIdiom() {
            if (this.cubes.length === 0) return null;
            return this.cubes[this.cubes.length - 1].idiom;
        }

        getTopCubesInfo() {
            if (this.cubes.length === 0) return { cubes: [], idiom: null };

            const topIdiom = this.getTopIdiom();
            let count = 0;
            for (let i = this.cubes.length - 1; i >= 0; i--) {
                if (this.cubes[i].idiom === topIdiom) {
                    count++;
                } else {
                    break;
                }
            }
            return {
                cubes: this.cubes.slice(this.cubes.length - count),
                idiom: topIdiom
            };
        }

        getEmptySpace() {
            return this.capacity - this.cubes.length;
        }

        isEmpty() {
            return this.cubes.length === 0;
        }

        addCubes(cubesToAdd) {
            this.cubes = this.cubes.concat(cubesToAdd);
            this.updateCubeVisuals();

            // 添加方块掉落动画 - 竖屏优化
            const newCubes = this.cubeSprites.slice(-cubesToAdd.length);
            newCubes.forEach((sprite, index) => {
                sprite.y -= 100; // 从更高处开始
                sprite.alpha = 0.7;
                sprite.scaleX = 0.8;
                sprite.scaleY = 0.8;

                this.scene.tweens.add({
                    targets: sprite,
                    y: -(this.cubes.length - cubesToAdd.length + index) * CUBE_SIZE - CUBE_SIZE/2,
                    alpha: 1,
                    scaleX: sprite.scaleX / 0.8,
                    scaleY: sprite.scaleY / 0.8,
                    duration: 280,
                    ease: 'Back.easeOut',
                    delay: index * 40, // 错开动画时间
                    onComplete: () => {
                        // 动画完成后更新完成状态高亮
                        this.updateCompletionHighlight();
                    }
                });
            });
        }

        removeCubes(count) {
            // 添加移除动画 - 竖屏优化
            const cubesToRemove = this.cubeSprites.slice(-count);
            cubesToRemove.forEach((sprite, index) => {
                this.scene.tweens.add({
                    targets: sprite,
                    alpha: 0,
                    y: sprite.y - 70,
                    scaleX: 0.6,
                    scaleY: 0.6,
                    rotation: (Math.random() - 0.5) * 0.4,
                    duration: 230,
                    delay: index * 25,
                    ease: 'Power2.easeIn',
                    onComplete: () => {
                        sprite.destroy();
                        // 最后一个动画完成后更新完成状态高亮
                        if (index === cubesToRemove.length - 1) {
                            this.updateCompletionHighlight();
                        }
                    }
                });
            });

            this.cubes.splice(this.cubes.length - count, count);
            this.cubeSprites.splice(-count, count);
        }

        isSortedAndFull() {
            if (this.cubes.length === 0) return false;
            if (this.cubes.length < this.capacity) return false;

            const firstIdiom = this.cubes[0].idiom;
            // 检查是否都是同一个成语
            if (!this.cubes.every(cube => cube.idiom === firstIdiom)) {
                return false;
            }

            // 检查是否按正确顺序组成成语（从上到下，所以需要反转检查）
            const idiomData = currentIdioms[firstIdiom];
            console.log(`检查列完成状态:`);
            console.log(`  成语: ${idiomData.idiom}`);
            console.log(`  期望顺序(从上到下): ${idiomData.characters.slice().reverse().join('')}`);
            console.log(`  实际顺序(从上到下): ${this.cubes.slice().reverse().map(c => c.character).join('')}`);

            // 从上到下检查，所以需要反转数组来检查
            const reversedCubes = this.cubes.slice().reverse();
            for (let i = 0; i < reversedCubes.length; i++) {
                if (reversedCubes[i].character !== idiomData.characters[i]) {
                    console.log(`  位置 ${i} 不匹配: 期望 '${idiomData.characters[i]}', 实际 '${reversedCubes[i].character}'`);
                    return false;
                }
            }

            console.log(`  列完成检查: 通过`);
            return true;
        }

        // 检查是否整列都是同一个成语的字符
        isSameIdiomColumn() {
            if (this.cubes.length === 0) return false;
            const firstIdiom = this.cubes[0].idiom;
            return this.cubes.every(cube => cube.idiom === firstIdiom);
        }



        highlight() {
            // 添加高亮效果 - 竖屏优化
            if (this.highlightGraphics) {
                this.highlightGraphics.destroy();
            }

            this.highlightGraphics = this.scene.add.graphics();

            // 外层高亮边框
            this.highlightGraphics.lineStyle(6, 0xFFD700, 1);
            this.highlightGraphics.strokeRoundedRect(-COLUMN_WIDTH/2 + 3, -COLUMN_HEIGHT + 35, COLUMN_WIDTH - 6, COLUMN_HEIGHT - 35, 6);

            // 内层发光效果
            this.highlightGraphics.lineStyle(3, 0xFFFFFF, 0.6);
            this.highlightGraphics.strokeRoundedRect(-COLUMN_WIDTH/2 + 6, -COLUMN_HEIGHT + 38, COLUMN_WIDTH - 12, COLUMN_HEIGHT - 38, 4);

            // 如果是同色列，添加特殊标识表示可以单个移动
            if (this.isSameIdiomColumn()) {
                this.highlightGraphics.lineStyle(3, 0x00FF00, 0.8);
                this.highlightGraphics.strokeRoundedRect(-COLUMN_WIDTH/2 + 9, -COLUMN_HEIGHT + 41, COLUMN_WIDTH - 18, COLUMN_HEIGHT - 41, 3);
            }

            this.add(this.highlightGraphics);

            // 添加脉冲效果
            this.scene.tweens.add({
                targets: this.highlightGraphics,
                alpha: 0.4,
                scaleX: 1.04,
                scaleY: 1.02,
                duration: 380,
                yoyo: true,
                repeat: -1,
                ease: 'Sine.easeInOut'
            });
        }

        unhighlight() {
            if (this.highlightGraphics) {
                this.highlightGraphics.destroy();
                this.highlightGraphics = null;
            }
        }

        // 更新完成状态高亮
        updateCompletionHighlight() {
            // 清除现有的完成高亮
            if (this.completionHighlight) {
                this.completionHighlight.destroy();
                this.completionHighlight = null;
            }

            // 如果列已完成，添加特殊高亮效果
            if (this.isSortedAndFull()) {
                this.completionHighlight = this.scene.add.graphics();

                // 获取成语对应的颜色
                const idiom = currentIdioms[this.cubes[0].idiom];
                const colors = idiom.colors;

                // 绘制完成状态的特殊边框
                this.completionHighlight.lineStyle(8, 0x00FF00, 0.9); // 绿色外边框
                this.completionHighlight.strokeRoundedRect(-COLUMN_WIDTH/2 + 1, -COLUMN_HEIGHT + 33, COLUMN_WIDTH - 2, COLUMN_HEIGHT - 33, 8);

                // 内层金色边框
                this.completionHighlight.lineStyle(4, 0xFFD700, 0.8);
                this.completionHighlight.strokeRoundedRect(-COLUMN_WIDTH/2 + 5, -COLUMN_HEIGHT + 37, COLUMN_WIDTH - 10, COLUMN_HEIGHT - 37, 6);

                // 添加发光效果
                this.completionHighlight.lineStyle(2, 0xFFFFFF, 0.6);
                this.completionHighlight.strokeRoundedRect(-COLUMN_WIDTH/2 + 9, -COLUMN_HEIGHT + 41, COLUMN_WIDTH - 18, COLUMN_HEIGHT - 41, 4);

                this.add(this.completionHighlight);

                // 添加闪烁动画
                this.scene.tweens.add({
                    targets: this.completionHighlight,
                    alpha: 0.3,
                    duration: 800,
                    yoyo: true,
                    repeat: -1,
                    ease: 'Sine.easeInOut'
                });

                // 添加缩放脉冲效果
                this.scene.tweens.add({
                    targets: this.completionHighlight,
                    scaleX: 1.02,
                    scaleY: 1.01,
                    duration: 1200,
                    yoyo: true,
                    repeat: -1,
                    ease: 'Sine.easeInOut'
                });
            }
        }

        getBounds() {
            return new Phaser.Geom.Rectangle(
                this.x - COLUMN_WIDTH/2,
                this.y - COLUMN_HEIGHT,
                COLUMN_WIDTH,
                COLUMN_HEIGHT
            );
        }
    }

    // 启动游戏
    window.onload = function() {
        game = new Phaser.Game(config);
    };
</script>

</body>
</html>
